package com.thelastblockbender.firenado;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.ThreadLocalRandom;

import org.bukkit.Location;
import org.bukkit.entity.ArmorStand;
import org.bukkit.entity.Entity;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.util.Vector;

import com.projectkorra.projectkorra.GeneralMethods;
import com.projectkorra.projectkorra.ProjectKorra;
import com.projectkorra.projectkorra.TLBMethods;
import com.projectkorra.projectkorra.ability.AddonAbility;
import com.projectkorra.projectkorra.ability.ComboAbility;
import com.projectkorra.projectkorra.ability.CoreAbility;
import com.projectkorra.projectkorra.ability.FireAbility;
import com.projectkorra.projectkorra.ability.util.ComboManager.AbilityInformation;
import com.projectkorra.projectkorra.command.Commands;
import com.projectkorra.projectkorra.configuration.ConfigManager;
import com.projectkorra.projectkorra.util.ClickType;
import com.projectkorra.projectkorra.util.DamageHandler;
import com.projectkorra.projectkorra.util.StatisticsMethods;

public class Firenado extends FireAbility implements AddonAbility, ComboAbility {
    private final Set<Entity> hitEntities = new HashSet<>();
    
    // Configuration variables
    private long cooldown;
    private double damage;
    private double speed;
    private double range;
    private double maxRadius;
    private double knockback;
    private double knockup;
    private int fireTicks;
    private int particleCount;
    private double spiralHeight;
    private double spiralSpeed;
    private long chargeTime;

    // Runtime variables
    private Location origin;
    private Location currentLocation;
    private Vector direction;
    private double currentRadius;
    private double currentHeight;
    private double angle;
    private double distance;
    private long currentLevel;

    // Charging variables
    private boolean charging;
    private boolean charged;
    private long chargeStartTime;

    public Firenado(Player player) {
        super(player);

        // Check if the ability is on cooldown
        if (bPlayer.isOnCooldown(this)) {
            return;
        }

        // Don't allow the move to start underwater
        if (player.getLocation().getBlock().getType().toString().contains("WATER")) {
            return;
        }

        setFields();
        origin = player.getEyeLocation().clone();
        direction = player.getEyeLocation().getDirection().normalize();
        currentLocation = origin.clone();
        currentRadius = 0.5;
        currentHeight = 0.5;
        angle = 0;
        distance = 0;

        // Initialize charging state
        charging = true;
        charged = false;
        chargeStartTime = System.currentTimeMillis();


        start();
    }
    
    private void setFields() {
        int statLevel = StatisticsMethods.getId("AbilityLevel_" + this.getName());
        currentLevel = TLBMethods.limitLevels(player, statLevel);
        
        this.cooldown = TLBMethods.getLong("ExtraAbilities.TLB.Firenado.Cooldown", currentLevel);
        this.damage = TLBMethods.getDouble("ExtraAbilities.TLB.Firenado.Damage", currentLevel);
        this.speed = TLBMethods.getDouble("ExtraAbilities.TLB.Firenado.Speed", currentLevel);
        this.range = TLBMethods.getDouble("ExtraAbilities.TLB.Firenado.Range", currentLevel);
        this.maxRadius = TLBMethods.getDouble("ExtraAbilities.TLB.Firenado.MaxRadius", currentLevel);
        this.knockback = TLBMethods.getDouble("ExtraAbilities.TLB.Firenado.Knockback", currentLevel);
        this.knockup = TLBMethods.getDouble("ExtraAbilities.TLB.Firenado.Knockup", currentLevel);
        this.fireTicks = TLBMethods.getInt("ExtraAbilities.TLB.Firenado.FireTicks", currentLevel);
        this.particleCount = TLBMethods.getInt("ExtraAbilities.TLB.Firenado.ParticleCount", currentLevel);
        this.spiralHeight = TLBMethods.getDouble("ExtraAbilities.TLB.Firenado.SpiralHeight", currentLevel);
        this.spiralSpeed = TLBMethods.getDouble("ExtraAbilities.TLB.Firenado.SpiralSpeed", currentLevel);
        this.chargeTime = TLBMethods.getLong("ExtraAbilities.TLB.Firenado.ChargeTime", currentLevel);
    }

    @Override
    public void progress() {

        if (!bPlayer.canBendIgnoreBindsCooldowns(this)) {
            remove();
            return;
        }

        if (charging) {
            // Charging phase
            if (!player.isSneaking()) {
                // Player released sneak before charge completed - cancel
                remove();
                return;
            }

            long chargeElapsed = System.currentTimeMillis() - chargeStartTime;
            if (chargeElapsed >= chargeTime) {
                // Charge complete
                charged = true;
                charging = false;

                // Show charge complete particle
                Location particleLoc = player.getEyeLocation().add(player.getEyeLocation().getDirection().multiply(1.5));
                playFirebendingParticles(particleLoc, 10, 0.2, 0.2, 0.2);
                FireAbility.playFirebendingSound(particleLoc);
            } else {
                // Show charging particles (dark grey dust)
                Location particleLoc = player.getEyeLocation().add(player.getEyeLocation().getDirection().multiply(1.0));
                double chargeProgress = (double) chargeElapsed / chargeTime;
                int particles = (int) (chargeProgress * 5) + 1;

                // Use dark grey dust particles during charging
                particleLoc.getWorld().spawnParticle(org.bukkit.Particle.DUST, particleLoc, particles, 0.1, 0.1, 0.1, 0,
                    new org.bukkit.Particle.DustOptions(org.bukkit.Color.fromRGB(64, 64, 64), 1.0f));
            }
            return;
        }

        if (charged && !player.isSneaking()) {
            // Player released sneak after charge - launch tornado
            charging = false;
            charged = false;
            // Put on cooldown immediately when tornado launches
            bPlayer.addCooldown(this);
            // Continue to tornado movement phase
        } else if (charged && player.isSneaking()) {
            // Still holding sneak after charge - wait for release
            Location particleLoc = player.getEyeLocation().add(player.getEyeLocation().getDirection().multiply(1.5));
            playFirebendingParticles(particleLoc, 8, 0.15, 0.15, 0.15);
            return;
        }

        // Tornado movement phase
        if (distance >= range) {
            remove();
            return;
        }

        advanceTornado();
        renderTornado();
        handleDamage();

        // Play sound occasionally
        if (ThreadLocalRandom.current().nextInt(5) == 0) {
            FireAbility.playFirebendingSound(currentLocation);
        }
    }

    private void advanceTornado() {
        // Update direction to follow player's current look direction
        direction = player.getEyeLocation().getDirection().normalize();

        // Move the tornado forward along the ground
        Vector moveDirection = direction.clone().multiply(speed);
        moveDirection.setY(0); // Keep it on the ground level
        currentLocation.add(moveDirection);
        distance += speed;

        // Keep the tornado at ground level
        currentLocation.setY(currentLocation.getWorld().getHighestBlockYAt(currentLocation) + 0.1);

        // Gradually increase radius and height throughout the entire travel distance
        double progressRatio = Math.min(distance / range, 1.0);
        currentRadius = 0.5 + (maxRadius - 0.5) * progressRatio;
        currentHeight = 0.5 + (spiralHeight - 0.5) * progressRatio;

        // Update angle for spiral rotation
        angle += spiralSpeed;
    }

    private boolean isUnderwater(Location location) {
        return location.getBlock().getType().toString().contains("WATER");
    }

    private void playTornadoParticles(Location location, int count, double offsetX, double offsetY, double offsetZ) {
        if (isUnderwater(location)) {
            // Use bubble particles underwater
            location.getWorld().spawnParticle(org.bukkit.Particle.BUBBLE_COLUMN_UP, location, count, offsetX, offsetY, offsetZ, 0.1);
        } else {
            // Use fire particles above water
            playFirebendingParticles(location, count, offsetX, offsetY, offsetZ);
        }
    }

    private void renderTornado() {
        // Create a single cone with spiral motion
        int levels = (int) Math.ceil(currentHeight * 4); // Fewer levels for cleaner cone

        for (int level = 0; level < levels; level++) {
            double levelHeight = (double) level / levels * currentHeight;
            // Create a right-side-up cone - narrower at bottom, wider at top
            double levelRadius = currentRadius * (0.2 + (levelHeight / currentHeight * 0.8));

            // Single spiral pattern for unified cone appearance
            int particlesAtLevel = Math.max(6, (int) (levelRadius * 8));

            for (int i = 0; i < particlesAtLevel; i++) {
                // Create single continuous spiral
                double spiralAngle = angle + (level * 0.5) + (i * 2 * Math.PI / particlesAtLevel);

                double x = Math.cos(spiralAngle) * levelRadius;
                double z = Math.sin(spiralAngle) * levelRadius;

                Location particleLoc = currentLocation.clone().add(x, levelHeight, z);

                // Minimal randomness to keep cone shape clean
                double randomOffset = 0.08;
                particleLoc.add(
                    (Math.random() - 0.5) * randomOffset,
                    (Math.random() - 0.5) * randomOffset * 0.3,
                    (Math.random() - 0.5) * randomOffset
                );

                // More particles at higher levels for cone density
                int particleAmount = Math.max(1, (int) (1 + (levelHeight / currentHeight * 1.5)));
                playTornadoParticles(particleLoc, particleAmount, 0.03, 0.03, 0.03);
            }
        }

        // Add base particles (narrow bottom of cone)
        playTornadoParticles(currentLocation, particleCount / 3, currentRadius * 0.15, 0.1, currentRadius * 0.15);

        // Add top particles (wide top of cone)
        Location topLoc = currentLocation.clone().add(0, currentHeight * 0.9, 0);
        playTornadoParticles(topLoc, particleCount / 2, currentRadius * 0.7, 0.1, currentRadius * 0.7);
    }

    private void handleDamage() {
        // Check for entities within the tornado's radius and height
        for (Entity entity : GeneralMethods.getEntitiesAroundPoint(currentLocation, currentRadius)) {
            if (entity instanceof LivingEntity && entity.getEntityId() != player.getEntityId() && !(entity instanceof ArmorStand)) {
                if (entity instanceof Player && Commands.invincible.contains(((Player) entity).getName())) {
                    continue;
                }

                // Check if entity is within the tornado's height
                double entityHeight = entity.getLocation().getY() - currentLocation.getY();
                if (entityHeight >= 0 && entityHeight <= currentHeight) {
                    if (!hitEntities.contains(entity)) {
                        hitEntities.add(entity);

                        // Damage the entity
                        DamageHandler.damageEntity(entity, player, damage, this);

                        // Set on fire
                        if (entity.getFireTicks() < fireTicks) {
                            entity.setFireTicks(fireTicks);
                        }

                        // Apply knockback in a spiral pattern
                        Vector knockbackDirection = entity.getLocation().toVector().subtract(currentLocation.toVector()).normalize();
                        knockbackDirection.setY(knockup);

                        // Add some rotational force for tornado effect
                        Vector rotationalForce = new Vector(-knockbackDirection.getZ(), 0, knockbackDirection.getX()).multiply(0.3);
                        knockbackDirection.add(rotationalForce);

                        entity.setVelocity(knockbackDirection.multiply(knockback));
                    }
                }
            }
        }
    }

    // Required ComboAbility methods
    @Override
    public ArrayList<AbilityInformation> getCombination() {
        ArrayList<AbilityInformation> combo = new ArrayList<>();
        combo.add(new AbilityInformation("FireBlast", ClickType.SHIFT_DOWN));
        combo.add(new AbilityInformation("FireBlast", ClickType.SHIFT_UP));
        combo.add(new AbilityInformation("HeatControl", ClickType.SHIFT_DOWN));
        return combo;
    }

    @Override
    public Object createNewComboInstance(Player player) {
        return new Firenado(player);
    }

    @Override
    public String getInstructions() {
        return "Fireblast (Tap Sneak) > HeatControl (Hold Sneak to charge, release to fire)";
    }

    // Required Ability methods
    @Override
    public long getCooldown() {
        return cooldown;
    }

    @Override
    public Location getLocation() {
        return currentLocation != null ? currentLocation : origin;
    }

    @Override
    public String getName() {
        return "Firenado";
    }

    @Override
    public boolean isHarmlessAbility() {
        return false;
    }

    @Override
    public boolean isSneakAbility() {
        return true;
    }

    @Override
    public boolean isHiddenAbility() {
        return false;
    }

    @Override
    public String getDescription() {
        return "Create a spiraling tornado of fire that grows larger and more powerful as it travels away from you. " +
               "The firenado damages and burns enemies and sends them flying";
    }

    @Override
    public String getAuthor() {
        return "TLB";
    }

    @Override
    public String getVersion() {
        return "1.0";
    }

    @Override
    public void load() {

        // Add default configuration values
        ConfigManager.getConfig().addDefault("ExtraAbilities.TLB.Firenado.Enabled", true);
        ConfigManager.getConfig().addDefault("ExtraAbilities.TLB.Firenado.Cooldown", 8000);
        ConfigManager.getConfig().addDefault("ExtraAbilities.TLB.Firenado.Damage", 3.0);
        ConfigManager.getConfig().addDefault("ExtraAbilities.TLB.Firenado.Speed", 0.8);
        ConfigManager.getConfig().addDefault("ExtraAbilities.TLB.Firenado.Range", 20.0);
        ConfigManager.getConfig().addDefault("ExtraAbilities.TLB.Firenado.MaxRadius", 4.0);
        ConfigManager.getConfig().addDefault("ExtraAbilities.TLB.Firenado.Knockback", 1.5);
        ConfigManager.getConfig().addDefault("ExtraAbilities.TLB.Firenado.Knockup", 0.8);
        ConfigManager.getConfig().addDefault("ExtraAbilities.TLB.Firenado.FireTicks", 60);
        ConfigManager.getConfig().addDefault("ExtraAbilities.TLB.Firenado.ParticleCount", 8);
        ConfigManager.getConfig().addDefault("ExtraAbilities.TLB.Firenado.SpiralHeight", 6.0);
        ConfigManager.getConfig().addDefault("ExtraAbilities.TLB.Firenado.SpiralSpeed", 0.3);
        ConfigManager.getConfig().addDefault("ExtraAbilities.TLB.Firenado.ChargeTime", 2000);
        ConfigManager.getConfig().addDefault("ExtraAbilities.TLB.Firenado.LingerDuration", 3000);

        ConfigManager.defaultConfig.save();
        ProjectKorra.getCollisionInitializer().addComboAbility((CoreAbility) this);
        ProjectKorra.log.info("Enabled " + this.getName() + " by " + this.getAuthor());
    }

    @Override
    public void stop() {
        ProjectKorra.log.info("Disabled " + this.getName() + " by " + this.getAuthor());
    }
}
