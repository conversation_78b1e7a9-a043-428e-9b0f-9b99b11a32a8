package com.thelastblockbender.spiritprojection;

import org.bukkit.Bukkit;
import org.bukkit.GameMode;
import org.bukkit.Location;

import com.denizenscript.denizen.objects.PlayerTag;
import com.denizenscript.denizencore.objects.Mechanism;
import com.denizenscript.denizencore.objects.core.ElementTag;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.event.entity.EntityDamageEvent;
import org.bukkit.event.entity.EntityDeathEvent;
import org.bukkit.entity.LivingEntity;

import net.citizensnpcs.api.CitizensAPI;
import net.citizensnpcs.api.npc.NPC;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.InventoryOpenEvent;
import org.bukkit.event.player.PlayerAnimationEvent;
import org.bukkit.event.player.PlayerInteractEntityEvent;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.event.player.PlayerQuitEvent;
import org.bukkit.event.player.PlayerTeleportEvent;
import org.bukkit.event.player.PlayerToggleSneakEvent;
import org.bukkit.event.player.PlayerGameModeChangeEvent;
import org.bukkit.event.entity.EntityTargetEvent;
import org.bukkit.potion.PotionEffectType;

import com.projectkorra.projectkorra.BendingPlayer;
import com.projectkorra.projectkorra.ability.CoreAbility;

public class SpiritProjectionListener implements Listener {

    @EventHandler
    public void onSneak(PlayerToggleSneakEvent event) {
        if (event.isCancelled()) {
            return;
        }
        
        Player player = event.getPlayer();
        BendingPlayer bPlayer = BendingPlayer.getBendingPlayer(player);
        
        if (bPlayer == null) {
            return;
        }
        
        // Check if player is starting the ability
        if (event.isSneaking() && bPlayer.getBoundAbilityName().equalsIgnoreCase("SpiritProjection")) {
            // Check if ability isn't already active
            if (!CoreAbility.hasAbility(player, SpiritProjection.class)) {
                new SpiritProjection(player);
            }
        }
    }
    
    // Prevent player interactions while in spirit projection
    @EventHandler(priority = EventPriority.HIGHEST)
    public void onPlayerInteract(PlayerInteractEvent event) {
        Player player = event.getPlayer();
        SpiritProjection projection = CoreAbility.getAbility(player, SpiritProjection.class);
        
        if (projection != null && projection.isProjectionActive()) {
            event.setCancelled(true);
        }
    }
    
    // Prevent entity interactions while in spirit projection
    @EventHandler(priority = EventPriority.HIGHEST)
    public void onPlayerInteractEntity(PlayerInteractEntityEvent event) {
        Player player = event.getPlayer();
        SpiritProjection projection = CoreAbility.getAbility(player, SpiritProjection.class);
        
        if (projection != null && projection.isProjectionActive()) {
            event.setCancelled(true);

            // Store the start location before ending projection
            Location startLoc = projection.getLocation();
            projection.endProjection();

            // Schedule a delayed teleport back to start location
            if (startLoc != null) {
                Bukkit.getScheduler().runTaskLater(Bukkit.getPluginManager().getPlugin("ProjectKorra"), () -> {
                    if (player.isOnline()) {
                        player.teleport(startLoc);
                    }
                }, 1L);
            }

            player.sendMessage("§cSpirit projection ended due to entity interaction!");
        }
    }
    
    // Prevent inventory interactions while in spirit projection
    @EventHandler(priority = EventPriority.HIGHEST)
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) {
            return;
        }
        
        Player player = (Player) event.getWhoClicked();
        SpiritProjection projection = CoreAbility.getAbility(player, SpiritProjection.class);
        
        if (projection != null && projection.isProjectionActive()) {
            event.setCancelled(true);
        }
    }
    
    // Prevent inventory opening while in spirit projection
    @EventHandler(priority = EventPriority.HIGHEST)
    public void onInventoryOpen(InventoryOpenEvent event) {
        if (!(event.getPlayer() instanceof Player)) {
            return;
        }
        
        Player player = (Player) event.getPlayer();
        SpiritProjection projection = CoreAbility.getAbility(player, SpiritProjection.class);
        
        if (projection != null && projection.isProjectionActive()) {
            event.setCancelled(true);
        }
    }
    
    // Prevent player animation/swing events while in spirit projection
    @EventHandler(priority = EventPriority.HIGHEST)
    public void onPlayerAnimation(PlayerAnimationEvent event) {
        Player player = event.getPlayer();
        SpiritProjection projection = CoreAbility.getAbility(player, SpiritProjection.class);
        
        if (projection != null && projection.isProjectionActive()) {
            event.setCancelled(true);
        }
    }
    
    // Prevent damage dealing while in spirit projection
    @EventHandler(priority = EventPriority.HIGHEST)
    public void onEntityDamageByEntity(EntityDamageByEntityEvent event) {
        if (!(event.getDamager() instanceof Player)) {
            return;
        }
        
        Player player = (Player) event.getDamager();
        SpiritProjection projection = CoreAbility.getAbility(player, SpiritProjection.class);
        
        if (projection != null && projection.isProjectionActive()) {
            event.setCancelled(true);
        }
    }
    
    // Prevent taking damage while in spirit projection (spectator mode should handle this, but extra safety)
    @EventHandler(priority = EventPriority.HIGHEST)
    public void onEntityDamage(EntityDamageEvent event) {
        if (!(event.getEntity() instanceof Player)) {
            return;
        }
        
        Player player = (Player) event.getEntity();
        SpiritProjection projection = CoreAbility.getAbility(player, SpiritProjection.class);
        
        if (projection != null && projection.isProjectionActive()) {
            event.setCancelled(true);
        }
    }

    // Handle player logout - end projection if active
    @EventHandler
    public void onPlayerQuit(PlayerQuitEvent event) {
        Player player = event.getPlayer();
        SpiritProjection projection = CoreAbility.getAbility(player, SpiritProjection.class);

        if (projection != null) {
            projection.remove(); // This will trigger cleanup
        }
    }

    // Prevent teleportation while in spirit projection
    @EventHandler(priority = EventPriority.HIGHEST)
    public void onPlayerTeleport(PlayerTeleportEvent event) {
        Player player = event.getPlayer();
        SpiritProjection projection = CoreAbility.getAbility(player, SpiritProjection.class);

        if (projection != null && projection.isProjectionActive()) {
            // Check if this is a teleportation caused by the spirit projection ability itself
            if (event.getCause() == PlayerTeleportEvent.TeleportCause.PLUGIN) {
                // Allow plugin-caused teleportation (like vertical movement restriction)
                return;
            }
            // For any other teleportation, end the spirit projection
            // Store the start location before ending projection
            Location startLoc = projection.getLocation();
            projection.endProjection();

            // Schedule a delayed teleport back to start location to override the original teleport
            if (startLoc != null) {
                Bukkit.getScheduler().runTaskLater(Bukkit.getPluginManager().getPlugin("ProjectKorra"), () -> {
                    if (player.isOnline()) {
                        player.teleport(startLoc);
                    }
                }, 1L);
            }
        }
    }

    // Prevent game mode changes while in spirit projection (except by the ability itself)
    @EventHandler(priority = EventPriority.HIGHEST)
    public void onGameModeChange(PlayerGameModeChangeEvent event) {
        Player player = event.getPlayer();
        SpiritProjection projection = CoreAbility.getAbility(player, SpiritProjection.class);

        if (projection != null && projection.isProjectionActive()) {
            // Only allow spectator mode (which the ability sets)
            if (event.getNewGameMode() != GameMode.SPECTATOR) {
                event.setCancelled(true);
                player.sendMessage("§cYou cannot change game mode while in spirit projection!");
            }
        }
    }

    // Handle damage to spirit projection body NPC
    @EventHandler(priority = EventPriority.HIGHEST)
    public void onNPCDamage(EntityDamageEvent event) {
        handleNPCDamage(event);
    }

    // Handle damage by entity to spirit projection body NPC
    @EventHandler(priority = EventPriority.HIGHEST)
    public void onNPCDamageByEntity(EntityDamageByEntityEvent event) {
        handleNPCDamage(event);
    }

    // Prevent spirit projection body NPC from dying
    @EventHandler(priority = EventPriority.HIGHEST)
    public void onNPCDeath(EntityDeathEvent event) {
        // Check if the dying entity is an NPC
        if (CitizensAPI.getNPCRegistry().isNPC(event.getEntity())) {
            NPC npc = CitizensAPI.getNPCRegistry().getNPC(event.getEntity());

            // Use the new tracking system to find the associated player
            Player player = SpiritProjection.getPlayerForNPC(npc);

            if (player != null) {
                // This is a spirit projection body NPC - prevent death
                event.setCancelled(true);

                // Set NPC health to very low but not dead
                if (npc.getEntity() != null && npc.getEntity() instanceof LivingEntity) {
                    ((LivingEntity) npc.getEntity()).setHealth(0.1);
                }
            }
        }
    }

    // Common method to handle NPC damage
    private void handleNPCDamage(EntityDamageEvent event) {
        // Check if the damaged entity is an NPC
        if (CitizensAPI.getNPCRegistry().isNPC(event.getEntity())) {
            NPC npc = CitizensAPI.getNPCRegistry().getNPC(event.getEntity());

            // Use the new tracking system to find the associated player
            Player player = SpiritProjection.getPlayerForNPC(npc);

            if (player != null && player.isOnline()) {
                SpiritProjection projection = CoreAbility.getAbility(player, SpiritProjection.class);

                if (projection != null && projection.isProjectionActive()) {
                    // Prevent the NPC from dying by setting health to max if it would die
                    if (npc.getEntity() instanceof LivingEntity) {
                        LivingEntity livingNPC = (LivingEntity) npc.getEntity();
                        if (event.getFinalDamage() >= livingNPC.getHealth()) {
                            // Cancel the death-dealing damage
                            event.setDamage(livingNPC.getHealth() - 0.1); // Leave at very low health but alive
                        }
                    }

                    // End the spirit projection when body takes damage
                    projection.endProjection();

                    // Send message to player
                    player.sendMessage("§cYour physical body was attacked! Spirit projection ended.");

                    // Additional safety: ensure player is in correct state
                    Bukkit.getScheduler().runTaskLater(Bukkit.getPluginManager().getPlugin("ProjectKorra"), () -> {
                        if (player.isOnline()) {
                            // Double-check game mode restoration
                            if (player.getGameMode() == GameMode.SPECTATOR) {
                                player.setGameMode(GameMode.SURVIVAL);
                            }
                        }
                    }, 5L);
                }
            }
        }
    }

    // Handle player login - cleanup any lingering effects
    @EventHandler
    public void onPlayerJoin(PlayerJoinEvent event) {
        Player player = event.getPlayer();

        // Remove any lingering darkness effect
        if (player.hasPotionEffect(PotionEffectType.DARKNESS)) {
            player.removePotionEffect(PotionEffectType.DARKNESS);
        }

        // Remove any lingering enderman vision effects from spirit projection
        // Use Denizen API to clear any special vision effects
        try {
            PlayerTag denizenPlayer = new PlayerTag(player);
            Mechanism visionMechanism = new Mechanism("vision", new ElementTag(""), null);
            denizenPlayer.adjust(visionMechanism);
        } catch (Exception e) {
        }

        // Remove any lingering potion effects as fallback
        if (player.hasPotionEffect(PotionEffectType.NIGHT_VISION)) {
            player.removePotionEffect(PotionEffectType.NIGHT_VISION);
        }

        // Ensure player is not in spectator mode unless they should be
        if (player.getGameMode() == GameMode.SPECTATOR) {
            // Check if they have an active spirit projection
            SpiritProjection projection = CoreAbility.getAbility(player, SpiritProjection.class);
            if (projection == null || !projection.isProjectionActive()) {
                // No active projection, set to survival mode
                player.setGameMode(GameMode.SURVIVAL);
            }
        }
    }
}
